-- section <PERSON><PERSON>EMA
DROP SCHEMA IF EXISTS app_support CASCADE
;

CREATE SCHEMA app_support
;

GRANT USAGE ON SCHEMA app_support TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_support TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_support TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_support TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_support
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_support
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_support
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor TICKET_STATUS
CREATE TYPE app_support.TICKET_STATUS AS ENUM(
  'open',
  'in_progress',
  'resolved',
  'closed'
)
;

-- !section
-- section TABLES
-- anchor ticket_category
CREATE TABLE app_support.ticket_category (
  id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  category_name TEXT NOT NULL UNIQUE
)
;

-- anchor ticket
CREATE TABLE app_support.ticket (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
  user_id UUID NOT NULL REFERENCES auth.users (id),
  problem_description TEXT NOT NULL,
  creation_date TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL,
  status app_support.TICKET_STATUS NOT NULL DEFAULT 'open',
  category_id INT NOT NULL REFERENCES app_support.ticket_category (id),
  resolution_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL
)
;

-- anchor ticket_comment
CREATE TABLE app_support.ticket_comment (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4 (),
  ticket_id UUID NOT NULL REFERENCES app_support.ticket (id),
  user_id UUID NOT NULL REFERENCES auth.users (id),
  COMMENT TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone ('utc'::TEXT, NOW()) NOT NULL
)
;

-- !section
-- section TRIGGERS
-- anchor ticket
CREATE TRIGGER on_update_ticket BEFORE
UPDATE ON app_support.ticket FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- !section
-- section INDEXES
-- anchor ticket
CREATE INDEX ticket_user_id_idx ON app_support.ticket (user_id)
;

CREATE INDEX ticket_category_id_idx ON app_support.ticket (category_id)
;

-- anchor ticket_comment
CREATE INDEX ticket_comment_ticket_id_idx ON app_support.ticket_comment (ticket_id)
;

CREATE INDEX ticket_comment_user_id_idx ON app_support.ticket_comment (user_id)
;

-- !section
-- section RLS POLICIES
-- anchor ticket_category
ALTER TABLE app_support.ticket_category ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "ticket_category_select_all" ON app_support.ticket_category FOR
SELECT
  USING (TRUE)
;

-- anchor ticket
ALTER TABLE app_support.ticket ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "ticket_select_all" ON app_support.ticket FOR
SELECT
  USING (TRUE)
;

-- anchor ticket_comment
ALTER TABLE app_support.ticket_comment ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "ticket_comment_select_all" ON app_support.ticket_comment FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "ticket_comment_insert_own" ON app_support.ticket_comment FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

-- !section