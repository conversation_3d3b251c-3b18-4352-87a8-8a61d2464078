import { describe, expect, test } from "vitest";
import { mockField } from "../mocks/app_catalog.field";
import { mockAdmin, mockCustomer, mockProvider } from "../mocks/auth.user";
import { createSetupHooks } from "./hooks/setup";
import { createRoleTestMatrix } from "./utils/matrix";

createSetupHooks();

const admin = mockAdmin();
const customer = mockCustomer();
const provider = mockProvider();

const testMatrix = createRoleTestMatrix<
  "admin" | "customer" | "provider",
  "field" | "option"
>({
  admin: {
    user: admin,
    permissions: {
      field: { view: true, insert: true, update: true, delete: true },
      option: { view: true, insert: true, update: true, delete: true }
    }
  },
  customer: {
    user: customer,
    permissions: {
      field: { view: true, insert: false, update: false, delete: false },
      option: { view: true, insert: false, update: false, delete: false }
    }
  },
  provider: {
    user: provider,
    permissions: {
      field: { view: true, insert: false, update: false, delete: false },
      option: { view: true, insert: false, update: false, delete: false }
    }
  }
});

const sharedField = mockField({
  admin,
  field: { type: "select" },
  options: [{ name: { en: "Shared Option" } }]
});

describe.each(testMatrix)("$role", ({ user, permissions }) => {
  describe("field", () => {
    const userField = mockField({ admin: user });
    const fieldToUpdate = mockField({ admin });
    const fieldToDelete = mockField({ admin });

    test(`can view: ${permissions.field.view}`, async () => {
      if (!user.client) throw new Error("User client is undefined");
      if (!sharedField.id) throw new Error("Field ID is undefined");

      const { data: viewField } = await user.client
        .schema("app_catalog")
        .from("field")
        .select()
        .eq("id", sharedField.id)
        .single();

      if (permissions.field.view) {
        expect(viewField?.id).toBe(sharedField.id);
      } else {
        expect(viewField).toBeNull();
      }
    });

    test(`can insert: ${permissions.field.insert}`, () => {
      if (permissions.field.insert) {
        expect(userField.id).toBeDefined();
      } else {
        expect(userField.id).toBeUndefined();
      }
    });

    test(`can update: ${permissions.field.update}`, async () => {
      if (!user.client) throw new Error("User client is undefined");
      if (!fieldToUpdate.id) throw new Error("Field ID is undefined");
      if (!admin.client) throw new Error("Admin client is undefined");

      await user.client
        .schema("app_catalog")
        .from("field")
        .update({ name: { en: "Modified Field" } })
        .eq("id", fieldToUpdate.id);

      const { data: checkField } = await admin.client
        .schema("app_catalog")
        .from("field")
        .select()
        .eq("id", fieldToUpdate.id)
        .single();

      if (permissions.field.update) {
        expect(checkField?.name).toEqual({ en: "Modified Field" });
      } else {
        expect(checkField?.name).toEqual({ en: "Test Field" });
      }
    });

    test(`can delete: ${permissions.field.delete}`, async () => {
      if (!user.client) throw new Error("User client is undefined");
      if (!fieldToDelete.id) throw new Error("Field ID is undefined");
      if (!admin.client) throw new Error("Admin client is undefined");

      await user.client
        .schema("app_catalog")
        .from("field")
        .delete()
        .eq("id", fieldToDelete.id);

      const { data: checkField } = await admin.client
        .schema("app_catalog")
        .from("field")
        .select()
        .eq("id", fieldToDelete.id)
        .single();

      if (permissions.field.delete) {
        expect(checkField).toBeNull();
      } else {
        expect(checkField?.id).toBe(fieldToDelete.id);
      }
    });
  });

  describe("field option", () => {
    const fieldWithNoOption = mockField({
      admin,
      field: { type: "select" },
      options: []
    });
    const fieldToUpdate = mockField({
      admin,
      field: { type: "select" },
      options: [{ name: { en: "Option to Update" } }]
    });
    const fieldToDelete = mockField({
      admin,
      field: { type: "select" },
      options: [{ name: { en: "Option to Delete" } }]
    });

    test(`can view: ${permissions.option.view}`, async () => {
      if (!user.client) throw new Error("User client is undefined");
      if (!sharedField.id) throw new Error("Field ID is undefined");
      if (!sharedField.options) throw new Error("Field options are undefined");

      const { data: viewOption } = await user.client
        .schema("app_catalog")
        .from("field_option")
        .select()
        .eq("field_id", sharedField.id)
        .single();

      if (permissions.option.view) {
        expect(viewOption?.id).toBe(sharedField.options[0].id);
      } else {
        expect(viewOption).toBeNull();
      }
    });

    test(`can insert: ${permissions.option.insert}`, async () => {
      if (!user.client) throw new Error("User client is undefined");
      if (!fieldWithNoOption.id) throw new Error("Field ID is undefined");

      const { data: insertOption } = await user.client
        .schema("app_catalog")
        .from("field_option")
        .insert({
          field_id: fieldWithNoOption.id,
          name: { en: "Inserted Option" }
        })
        .select()
        .single();

      if (permissions.option.insert) {
        expect(insertOption?.id).toBeDefined();
      } else {
        expect(insertOption).toBeNull();
      }
    });

    test(`can update: ${permissions.option.update}`, async () => {
      if (!user.client) throw new Error("User client is undefined");
      if (!fieldToUpdate.id) throw new Error("Field ID is undefined");
      if (!fieldToUpdate.options)
        throw new Error("Field options are undefined");
      if (!admin.client) throw new Error("Admin client is undefined");

      await user.client
        .schema("app_catalog")
        .from("field_option")
        .update({ name: { en: "Modified Option" } })
        .eq("id", fieldToUpdate.options[0].id);

      const { data: checkOption } = await admin.client
        .schema("app_catalog")
        .from("field_option")
        .select()
        .eq("id", fieldToUpdate.options[0].id)
        .single();

      if (permissions.option.update) {
        expect(checkOption?.name).toEqual({ en: "Modified Option" });
      } else {
        expect(checkOption?.name).toEqual({ en: "Option to Update" });
      }
    });

    test(`can delete: ${permissions.option.delete}`, async () => {
      if (!user.client) throw new Error("User client is undefined");
      if (!fieldToDelete.id) throw new Error("Field ID is undefined");
      if (!fieldToDelete.options)
        throw new Error("Field options are undefined");
      if (!admin.client) throw new Error("Admin client is undefined");

      await user.client
        .schema("app_catalog")
        .from("field_option")
        .delete()
        .eq("id", fieldToDelete.options[0].id);

      const { data: checkOption } = await admin.client
        .schema("app_catalog")
        .from("field_option")
        .select()
        .eq("id", fieldToDelete.options[0].id)
        .single();

      if (permissions.option.delete) {
        expect(checkOption).toBeNull();
      } else {
        expect(checkOption?.id).toBe(fieldToDelete.options[0].id);
      }
    });
  });
});

describe("Field Option Constraints", () => {
  const selectField = mockField({
    admin,
    field: { type: "select" },
    options: [{ name: { en: "Option 1" } }]
  });

  const multiSelectField = mockField({
    admin,
    field: { type: "multiselect" },
    options: [{ name: { en: "Option 1" } }]
  });

  const textField = mockField({
    admin,
    field: { type: "text" },
    options: [{ name: { en: "Option 1" } }]
  });

  const checkboxField = mockField({
    admin,
    field: { type: "checkbox" },
    options: [{ name: { en: "Option 1" } }]
  });

  test("can only add one option to a select field", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!selectField.id) throw new Error("Field ID is undefined");

    const { error: insertError } = await admin.client
      .schema("app_catalog")
      .from("field_option")
      .insert({
        field_id: selectField.id,
        name: { en: "Option 2" }
      });

    expect(insertError).not.toBeNull();
  });

  test("can add multiple options to a multiselect field", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!multiSelectField.id) throw new Error("Field ID is undefined");

    const { error: insertError } = await admin.client
      .schema("app_catalog")
      .from("field_option")
      .insert({
        field_id: multiSelectField.id,
        name: { en: "Option 2" }
      });

    expect(insertError).toBeNull();
  });

  test("cannot add options to a text field", async () => {
    expect(textField.options).toBeNull();
  });

  test("cannot add options to a checkbox field", async () => {
    expect(checkboxField.options).toBeNull();
  });
});
