import { test, expect, describe, beforeAll } from "vitest";
import {
  mockApplication,
  mockFilledOutApplication
} from "../mocks/app_provider.application";
import { mockAdmin, mockCustomer } from "../mocks/auth.user";
import { createSetupHooks } from "./hooks/setup";
import { serviceClient } from "../utils/client";

createSetupHooks();

const admin = mockAdmin();

describe("role", () => {
  describe("customer", () => {
    const customer = mockCustomer();
    mockApplication(customer);

    test("cannot submit application twice", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .insert({
          user_id: customer.data.id,
          application_status: "draft"
        });

      expect(submitApplication.error).not.toBeNull();
    });

    test("still has customer role", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");

      const { data: hasRole } = await customer.client
        .schema("app_access")
        .rpc("has_role", {
          role_name: "customer"
        });

      expect(hasRole).toBe(true);
    });

    test("acquires `provider_applicant` role on application creation", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");

      const { data: hasRole } = await customer.client
        .schema("app_access")
        .rpc("has_role", {
          role_name: "provider_applicant"
        });

      expect(hasRole).toBe(true);
    });

    test("cannot submit application if no profile and service created", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      // submit application
      const applicationUpdate = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).not.toBeNull();
    });
  });

  describe("provider_applicant", () => {
    const customer = mockCustomer();
    const filledOutApplication = mockFilledOutApplication(customer);

    test("can update profile, service and service modifier", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!filledOutApplication.service.providerServiceId) {
        throw new Error("Service ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceModifierId) {
        throw new Error("Service Modifier ID is undefined");
      }

      const updateProfile = await customer.client
        .schema("app_provider")
        .from("profile")
        .update({ bio: { en: "Updated bio" } })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(updateProfile.data?.bio).toEqual({ en: "Updated bio" });

      const updateService = await customer.client
        .schema("app_provider")
        .from("service")
        .update({ name: { en: "Updated Service" } })
        .eq("id", filledOutApplication.service.providerServiceId)
        .select("*")
        .single();

      expect(updateService.data?.name).toEqual({ en: "Updated Service" });

      const updateServiceModifier = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .update({ name: { en: "Updated Modifier" } })
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .select("*")
        .single();

      expect(updateServiceModifier.data?.name).toEqual({
        en: "Updated Modifier"
      });
    });

    test("can submit application", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(submitApplication.data?.application_status).toBe("submitted");
    });
  });

  describe("provider_applicant_under_review", () => {
    const customer = mockCustomer();
    const filledOutApplication = mockFilledOutApplication(customer);

    beforeAll(async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      const submitApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(submitApplication.data?.application_status).toBe("submitted");
    });

    test("can view application, profile, activity, service and service modifier", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!filledOutApplication.service.providerServiceId) {
        throw new Error("Service ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceModifierId) {
        throw new Error("Service Modifier ID is undefined");
      }
      if (!filledOutApplication.service.selectedActivityId) {
        throw new Error("Activity ID is undefined");
      }

      const viewApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(viewApplication.data?.application_status).toBe("submitted");

      const viewProfile = await customer.client
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(viewProfile.data?.user_id).toBe(customer.data.id);

      const viewActivity = await customer.client
        .schema("app_provider")
        .from("activity")
        .select("*")
        .eq("id", filledOutApplication.service.selectedActivityId)
        .single();

      expect(viewActivity.data?.id).toBe(
        filledOutApplication.service.selectedActivityId
      );

      const viewService = await customer.client
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceId)
        .single();

      expect(viewService.data?.id).toBe(
        filledOutApplication.service.providerServiceId
      );

      const viewServiceModifier = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .single();

      expect(viewServiceModifier.data?.id).toBe(
        filledOutApplication.service.providerServiceModifierId
      );
    });

    test("cannot update or delete, application, profile, activity, service and service modifier", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!filledOutApplication.service.selectedActivityId) {
        throw new Error("Activity ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceId) {
        throw new Error("Service ID is undefined");
      }
      if (!filledOutApplication.service.providerServiceModifierId) {
        throw new Error("Service Modifier ID is undefined");
      }

      await customer.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "draft" })
        .eq("user_id", customer.data.id);

      await customer.client
        .schema("app_provider")
        .from("application")
        .delete()
        .eq("user_id", customer.data.id);

      const checkApplication = await customer.client
        .schema("app_provider")
        .from("application")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(checkApplication.data?.application_status).toBe("submitted");

      await customer.client
        .schema("app_provider")
        .from("profile")
        .update({ bio: { en: "Updated bio" } })
        .eq("user_id", customer.data.id);

      const checkProfileUpdate = await customer.client
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(checkProfileUpdate.data?.bio).not.toEqual({ en: "Updated bio" });

      // delete profile
      await customer.client
        .schema("app_provider")
        .from("profile")
        .delete()
        .eq("user_id", customer.data.id);

      const checkProfileDelete = await customer.client
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", customer.data.id)
        .single();

      expect(checkProfileDelete.data).toBeDefined();

      // delete activity
      await customer.client
        .schema("app_provider")
        .from("activity")
        .delete()
        .eq("id", filledOutApplication.service.selectedActivityId);

      const checkActivityDelete = await customer.client
        .schema("app_provider")
        .from("activity")
        .select("*")
        .eq("id", filledOutApplication.service.selectedActivityId)
        .single();

      expect(checkActivityDelete.data).toBeDefined();

      // update service
      await customer.client
        .schema("app_provider")
        .from("service")
        .update({ soda_amount: 66 })
        .eq("id", filledOutApplication.service.providerServiceId);

      const checkServiceUpdate = await customer.client
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceId)
        .single();

      expect(checkServiceUpdate.data?.soda_amount).not.toBe(66);

      // delete service
      await customer.client
        .schema("app_provider")
        .from("service")
        .delete()
        .eq("id", filledOutApplication.service.providerServiceId);

      const checkServiceDelete = await customer.client
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceId)
        .single();

      expect(checkServiceDelete.data).toBeDefined();

      const updateServiceModifier = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .update({ soda_amount: 66 })
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .select()
        .single();

      expect(updateServiceModifier.data?.soda_amount).not.toBe(66);

      // delete service modifier
      await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .delete()
        .eq("id", filledOutApplication.service.providerServiceModifierId);

      const checkServiceModifierDelete = await customer.client
        .schema("app_provider")
        .from("service_modifier")
        .select("*")
        .eq("id", filledOutApplication.service.providerServiceModifierId)
        .single();

      expect(checkServiceModifierDelete.data).toBeDefined();
    });

    test("role switches to `provider` when application is approved", async () => {
      if (!admin.client) throw new Error("Admin client is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const applicationUpdate = await admin.client
        .schema("app_provider")
        .from("application")
        .update({ application_status: "approved" })
        .eq("user_id", customer.data.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).toBeNull();
      expect(applicationUpdate.data?.application_status).toBe("approved");

      const { data: hasProviderRole } = await customer.client
        .schema("app_access")
        .rpc("has_role", {
          role_name: "provider"
        });

      expect(hasProviderRole).toBe(true);
    });
  });
});

describe("application rejection", () => {
  const customer = mockCustomer();
  mockFilledOutApplication(customer);

  // submit application
  beforeAll(async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!admin.client) throw new Error("Admin client is undefined");

    const submitApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .update({ application_status: "submitted" })
      .eq("user_id", customer.data.id)
      .select("*")
      .single();

    expect(submitApplication.data?.application_status).toBe("submitted");

    const rejectApplication = await admin.client
      .schema("app_provider")
      .from("application")
      .update({ application_status: "rejected" })
      .eq("user_id", customer.data.id)
      .select("*")
      .single();

    expect(rejectApplication.error).toBeNull();
    expect(rejectApplication.data?.application_status).toBe("rejected");
  });

  test("application roles are removed when application is rejected", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const { data: hasProviderRole } = await customer.client
      .schema("app_access")
      .rpc("has_role", {
        role_name: "provider"
      });

    expect(hasProviderRole).toBe(false);

    const { data: hasProviderApplicantRole } = await customer.client
      .schema("app_access")
      .rpc("has_role", {
        role_name: "provider_applicant"
      });

    expect(hasProviderApplicantRole).toBe(false);

    const { data: hasProviderApplicantUnderReviewRole } = await customer.client
      .schema("app_access")
      .rpc("has_role", {
        role_name: "provider_applicant_under_review"
      });

    expect(hasProviderApplicantUnderReviewRole).toBe(false);
  });

  test("provider data is deleted when application is rejected", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    const profileSelect = await serviceClient
      .schema("app_provider")
      .from("profile")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(profileSelect.data?.length).toBe(0);

    const activitySelect = await serviceClient
      .schema("app_provider")
      .from("activity")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(activitySelect.data?.length).toBe(0);

    const serviceSelect = await serviceClient
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(serviceSelect.data?.length).toBe(0);

    const serviceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .select("*")
      .eq("user_id", customer.data.id);
    expect(serviceModifierSelect.data?.length).toBe(0);
  });
});

describe("application approved", () => {
  const customer = mockCustomer();
  const filledOutApplication = mockFilledOutApplication(customer);

  // approve application
  beforeAll(async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const approveApplication = await admin.client
      .schema("app_provider")
      .from("application")
      .update({ application_status: "approved" })
      .eq("user_id", customer.data.id)
      .select("*")
      .single();

    expect(approveApplication.data?.application_status).toBe("approved");
  });

  test("service and service modifier are approved and their status are set to published", async () => {
    if (!filledOutApplication.service.providerServiceId) {
      throw new Error("Service ID is undefined");
    }
    if (!filledOutApplication.service.providerServiceModifierId) {
      throw new Error("Service Modifier ID is undefined");
    }

    const serviceSelect = await serviceClient
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("id", filledOutApplication.service.providerServiceId)
      .single();

    expect(serviceSelect.data?.status).toBe("published");

    const serviceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .select("*")
      .eq("id", filledOutApplication.service.providerServiceModifierId)
      .single();

    expect(serviceModifierSelect.data?.status).toBe("published");

    const approvedServiceSelect = await serviceClient
      .schema("app_provider")
      .from("approved_service")
      .select("*")
      .eq("service_id", filledOutApplication.service.providerServiceId)
      .single();

    expect(approvedServiceSelect.data?.service_id).toBe(
      filledOutApplication.service.providerServiceId
    );

    const approvedServiceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("approved_service_modifier")
      .select("*")
      .eq(
        "service_modifier_id",
        filledOutApplication.service.providerServiceModifierId
      )
      .single();

    expect(approvedServiceModifierSelect.data?.service_modifier_id).toBe(
      filledOutApplication.service.providerServiceModifierId
    );
  });
});

describe("prohibited", () => {
  const customer = mockCustomer();

  test("cannot insert with `submitted` status", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");

    const submitApplication = await customer.client
      .schema("app_provider")
      .from("application")
      .insert({
        user_id: customer.data.id,
        application_status: "submitted"
      });

    expect(submitApplication.error).not.toBeNull();
  });
});
