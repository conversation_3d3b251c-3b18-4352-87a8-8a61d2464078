-- section <PERSON><PERSON>EM<PERSON>
DROP SCHEMA IF EXISTS app_account CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_account
;

GRANT USAGE ON SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor KYC_STATUS
CREATE TYPE app_account.KYC_STATUS AS ENUM(
  'draft',
  'pending',
  'approved',
  'rejected'
)
;

-- !section
-- section TABLES
-- anchor profile
CREATE TABLE app_account.profile (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  NAME TEXT NOT NULL CHECK (
    LENGTH(NAME) <= 10
    AND NAME ~ '^[a-zA-Z0-9_-]+(?: [a-zA-Z0-9_-]+)*$'
  ),
  honorific app_core.honorific,
  gender app_core.gender,
  join_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  birth_date DATE,
  CHECK (
    birth_date <= NOW() - INTERVAL '18 years'
  )
)
;

-- anchor locale
CREATE TABLE app_account.locale (
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  locale app_core.locale,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, locale)
)
;

-- anchor kyc
CREATE TABLE app_account.kyc (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID UNIQUE REFERENCES auth.users (id) ON DELETE CASCADE,
  status app_account.KYC_STATUS NOT NULL DEFAULT 'pending',
  full_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor iban
CREATE TABLE app_account.iban (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  iban TEXT NOT NULL CHECK (
    iban ~ '^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$'
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor privacy
CREATE TABLE app_account.privacy (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  show_profile BOOLEAN NOT NULL DEFAULT TRUE,
  show_activity BOOLEAN NOT NULL DEFAULT TRUE,
  show_age BOOLEAN NOT NULL DEFAULT TRUE,
  show_gender BOOLEAN NOT NULL DEFAULT TRUE,
  show_in_leaderboard BOOLEAN NOT NULL DEFAULT TRUE
)
;

-- !section
-- section TRIGGERS
-- anchor kyc
CREATE TRIGGER kyc_set_updated_at BEFORE
UPDATE ON app_account.kyc FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor iban
CREATE TRIGGER iban_set_updated_at BEFORE
UPDATE ON app_account.iban FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- !section
-- section RLS POLICIES
-- anchor profile
ALTER TABLE app_account.profile ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "profile_select_all" ON app_account.profile FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "profile_insert_own" ON app_account.profile FOR INSERT
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "profile_update_own" ON app_account.profile
FOR UPDATE
  USING (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

-- anchor locale
ALTER TABLE app_account.locale ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "locale_select_own" ON app_account.locale FOR
SELECT
  USING (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "locale_insert_own" ON app_account.locale FOR INSERT
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "locale_update_own" ON app_account.locale
FOR UPDATE
  USING (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "locale_delete_own" ON app_account.locale FOR DELETE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
)
;

-- anchor kyc
ALTER TABLE app_account.kyc ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "kyc_select_own_draft_pending" ON app_account.kyc FOR
SELECT
  USING (
    user_id = (
      SELECT
        auth.uid ()
    )
    AND (
      status = 'draft'
      OR status = 'pending'
    )
  )
;

CREATE POLICY "kyc_insert_own_draft_pending" ON app_account.kyc FOR INSERT
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
    AND (
      status = 'draft'
      OR status = 'pending'
    )
  )
;

CREATE POLICY "kyc_update_own_draft_pending" ON app_account.kyc
FOR UPDATE
  USING (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
    AND (
      status = 'draft'
      OR status = 'pending'
    )
  )
;

CREATE POLICY "kyc_delete_own_draft_pending" ON app_account.kyc FOR DELETE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
  AND (
    status = 'draft'
    OR status = 'pending'
  )
)
;

-- anchor iban
ALTER TABLE app_account.iban ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "iban_select_own" ON app_account.iban FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "iban_insert_own" ON app_account.iban FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "iban_update_own" ON app_account.iban
FOR UPDATE
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "iban_delete_own" ON app_account.iban FOR DELETE USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
)
;

-- anchor privacy
ALTER TABLE app_account.privacy ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "privacy_select_own" ON app_account.privacy FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "privacy_insert_own" ON app_account.privacy FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "privacy_update_own" ON app_account.privacy
FOR UPDATE
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

-- !section