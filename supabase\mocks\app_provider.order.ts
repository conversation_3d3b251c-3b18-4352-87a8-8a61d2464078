import { afterAll, afterEach, beforeAll, beforeEach, expect } from "vitest";
import { serviceClient } from "../utils/client";
import { MockUser } from "./auth.user";
import { MockService } from "./app_provider.service";
import { Database } from "shared/lib/supabase/database";

export type MockOrder = {
  id?: string;
  data?: Database["app_provider"]["Tables"]["order"]["Row"] | null;
};

type MockOrderParams =
  | {
      unit_count?: number;
      status?: Database["app_provider"]["Enums"]["order_status"];
      service: MockService;
      customer: MockUser;
      provider: MockUser;
      admin?: undefined;
    }
  | {
      unit_count?: number;
      status: "refunded_by_admin" | "released_by_admin";
      service: MockService;
      customer: MockUser;
      provider: MockUser;
      admin: MockUser;
    };

async function setupOrder(
  {
    unit_count = 1,
    status = "pending",
    customer,
    provider,
    service,
    admin
  }: MockOrderParams,
  order: MockOrder
) {
  if (!customer.data) throw new Error("User data is undefined");
  if (!customer.client) throw new Error("User client is undefined");
  if (!provider.data) throw new Error("Provider data is undefined");
  if (!provider.client) throw new Error("Provider client is undefined");
  if (!service.providerServiceId) throw new Error("Service data is undefined");
  if (!service.providerServiceModifierId)
    throw new Error("Service modifier data is undefined");

  // User submits an order
  const submitOrder = await customer.client
    .schema("app_provider")
    .rpc("submit_order", {
      p_service_id: service.providerServiceId,
      p_unit_count: unit_count,
      p_service_modifier_ids: [service.providerServiceModifierId]
    });

  expect(submitOrder.data?.order_status).toBe("pending");

  order.id = String(submitOrder.data?.id);
  order.data = submitOrder.data;

  if (status === "pending") return;

  if (status === "cancelled") {
    // User cancels the order
    const cancelUpdate = await customer.client
      .schema("app_provider")
      .from("order")
      .update({ order_status: "cancelled" })
      .eq("id", order.id)
      .select()
      .single();

    expect(cancelUpdate?.data?.order_status).toBe("cancelled");

    return;
  }

  switch (status) {
    case "rejected": {
      // Provider rejects the order
      const rejectUpdate = await provider.client
        .schema("app_provider")
        .from("order")
        .update({ order_status: "rejected" })
        .eq("id", order.id)
        .select()
        .single();

      expect(rejectUpdate?.data?.order_status).toBe("rejected");

      return;
    }

    default: {
      // Provider accepts the order
      const acceptUpdate = await provider.client
        .schema("app_provider")
        .from("order")
        .update({ order_status: "accepted" })
        .eq("id", order.id)
        .select()
        .single();

      expect(acceptUpdate?.data?.order_status).toBe("accepted");

      if (status === "accepted") return;
    }
  }

  // Provider completes the order
  const completeUpdate = await provider.client
    .schema("app_provider")
    .from("order")
    .update({ order_status: "completed" })
    .eq("id", order.id)
    .select()
    .single();

  expect(completeUpdate?.data?.order_status).toBe("completed");

  if (status === "completed") return;

  // User disputes the order
  const disputeUpdate = await customer.client
    .schema("app_provider")
    .from("order")
    .update({ order_status: "in_dispute" })
    .eq("id", order.id)
    .select()
    .single();

  expect(disputeUpdate?.data?.order_status).toBe("in_dispute");

  if (status === "in_dispute") return;

  switch (status) {
    case "refunded": {
      // Provider refunds the order
      const refundUpdate = await provider.client
        .schema("app_provider")
        .from("order")
        .update({ order_status: "refunded" })
        .eq("id", order.id)
        .select()
        .single();

      expect(refundUpdate?.data?.order_status).toBe("refunded");

      return;
    }
    case "refunded_by_admin": {
      if (!admin?.client) throw new Error("Admin client is undefined");

      // Admin refunds the order
      const refundUpdate = await admin.client
        .schema("app_provider")
        .rpc("handle_disputed_order", {
          p_order_id: order.id,
          p_action: "refund"
        });

      expect(refundUpdate.error).toBeNull();

      return;
    }
    case "released_by_admin": {
      if (!admin?.client) throw new Error("Admin client is undefined");

      // Admin releases the order
      const releaseUpdate = await admin.client
        .schema("app_provider")
        .rpc("handle_disputed_order", {
          p_order_id: order.id,
          p_action: "release"
        });

      expect(releaseUpdate.error).toBeNull();

      return;
    }

    default:
      return;
  }
}

async function cleanupOrder(order: MockOrder) {
  if (!order.id) throw new Error("Order ID not set for cleaning");

  // Clean up orders
  await serviceClient
    .schema("app_provider")
    .from("order")
    .delete()
    .eq("id", order.id);

  // Clean up order archive
  await serviceClient
    .schema("app_provider")
    .from("order_archive")
    .delete()
    .eq("id", order.id);

  // Clean up order logs
  await serviceClient
    .schema("app_provider")
    .from("order_log")
    .delete()
    .eq("order_id", order.id);
}

export function mockOrder(params: MockOrderParams) {
  const order: MockOrder = {};

  beforeAll(async () => {
    await setupOrder(params, order);
  });

  afterAll(async () => {
    await cleanupOrder(order);
  });

  return order;
}

export function mockOrderEach(params: MockOrderParams) {
  const order: MockOrder = {};

  beforeEach(async () => {
    await setupOrder(params, order);
  });

  afterEach(async () => {
    await cleanupOrder(order);
  });

  return order;
}
