import { beforeAll, afterAll, afterEach, beforeEach } from "vitest";
import { dbClient } from "../../utils/client";
import { resetRateLimits } from "../utils/rate-limit";

export function createSetupHooks() {
  beforeAll(async () => {
    await dbClient.connect();

    await resetRateLimits();
  });

  beforeEach(async () => {
    await resetRateLimits();
  });

  afterEach(async () => {
    await resetRateLimits();
  });

  afterAll(async () => {
    await resetRateLimits();

    await dbClient.end();
  });
}
