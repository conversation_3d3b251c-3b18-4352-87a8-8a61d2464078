import { SupabaseClient, User } from "@supabase/supabase-js";
import { Database } from "shared/lib/supabase/database";
import { createClient } from "@supabase/supabase-js";
import { afterAll, beforeAll, expect } from "vitest";
import { randomUUID } from "node:crypto";
import { serviceClient } from "../utils/client";

export type MockUser = {
  client?: SupabaseClient<Database>;
  data?: User;
};

export async function createAndSignInUser(role: string): Promise<MockUser> {
  const userId = randomUUID();

  const userCreation = await serviceClient.auth.admin.createUser({
    id: userId,
    email: `${role}-${userId}@example.com`,
    password: "password123",
    email_confirm: true
  });

  if (!userCreation.data.user) {
    throw `Failed to create ${role} user`;
  }

  // assign role to user
  await serviceClient.schema("app_access").rpc("assign_role_to_user", {
    v_user_id: userCreation.data.user.id,
    v_role_name: role
  });

  const client = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  await client.auth.signInWithPassword({
    email: userCreation.data.user.email ?? "",
    password: "password123"
  });

  return { client, data: userCreation.data.user };
}

export function mockUser(role: string) {
  const user: MockUser = {};

  beforeAll(async () => {
    const { client, data } = await createAndSignInUser(role);

    user.client = client;
    user.data = data;
  });

  afterAll(async () => {
    if (user.data) {
      await serviceClient.auth.admin.deleteUser(user.data.id);
    }
  });

  return user;
}

export function mockAdmin() {
  return mockUser("admin");
}

export function mockCustomer(balance = 1000) {
  const customer = mockUser("customer");

  beforeAll(async () => {
    if (customer.data)
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .upsert({ user_id: customer.data.id, soda_balance: balance })
        .select();
  });

  return customer;
}

export function mockProvider() {
  const provider = mockUser("customer");

  beforeAll(async () => {
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!provider.client) throw new Error("Provider client is undefined");

    const approveProvider = await serviceClient
      .schema("app_provider")
      .from("approved_user")
      .insert({ user_id: provider.data.id })
      .select()
      .single();

    expect(approveProvider.data?.user_id).toBe(provider.data.id);

    const setOpenForOrder = await provider.client
      .schema("app_provider")
      .from("status")
      .insert({
        user_id: provider.data.id,
        is_open_for_orders: true
      })
      .select()
      .single();

    expect(setOpenForOrder.data?.user_id).toBe(provider.data.id);
  });

  return provider;
}

export function mockProviderApplicant() {
  return mockUser("provider_applicant");
}
