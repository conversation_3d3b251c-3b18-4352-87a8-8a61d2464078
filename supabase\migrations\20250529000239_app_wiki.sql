-- section <PERSON><PERSON>EMA
DROP SCHEMA IF EXISTS app_wiki CASCADE
;

CREATE SCHEMA app_wiki
;

GRANT USAGE ON SCHEMA app_wiki TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_wiki TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_wiki TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_wiki TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_wiki
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_wiki
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_wiki
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section TABLES
-- anchor document
CREATE TABLE app_wiki.document (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  description JSONB NOT NULL,
  CONTENT JSONB NOT NULL
)
;

ALTER TABLE app_wiki.document ENABLE ROW LEVEL SECURITY
;

-- anchor changelog
CREATE TABLE app_wiki.changelog (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  VERSION TEXT NOT NULL,
  CONTENT JSONB NOT NULL
)
;

ALTER TABLE app_wiki.changelog ENABLE ROW LEVEL SECURITY
;

-- anchor rule
CREATE TABLE app_wiki.rule (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  CONTENT JSONB NOT NULL,
  category TEXT
)
;

ALTER TABLE app_wiki.rule ENABLE ROW LEVEL SECURITY
;

-- anchor news
CREATE TABLE app_wiki.news (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  description JSONB NOT NULL,
  CONTENT JSONB NOT NULL,
  published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  author_id UUID REFERENCES auth.users (id) ON DELETE SET NULL
)
;

ALTER TABLE app_wiki.news ENABLE ROW LEVEL SECURITY
;

-- anchor event
CREATE TABLE app_wiki.event (
  slug app_core.SLUG PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  title JSONB NOT NULL,
  description JSONB NOT NULL,
  CONTENT JSONB NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL
)
;

ALTER TABLE app_wiki.event ENABLE ROW LEVEL SECURITY
;

-- !section
-- section TRIGGERS
-- anchor document
CREATE TRIGGER document_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.document FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns (
  'title',
  'description',
  'content'
)
;

CREATE TRIGGER document_set_updated_at BEFORE
UPDATE ON app_wiki.document FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor changelog
CREATE TRIGGER changelog_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.changelog FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('content')
;

-- anchor rule
CREATE TRIGGER rule_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.rule FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('title', 'content')
;

CREATE TRIGGER rule_set_updated_at BEFORE
UPDATE ON app_wiki.rule FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor news
CREATE TRIGGER news_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.news FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns (
  'title',
  'description',
  'content'
)
;

CREATE TRIGGER news_set_updated_at BEFORE
UPDATE ON app_wiki.news FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor event
CREATE TRIGGER event_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_wiki.event FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns (
  'title',
  'description',
  'content'
)
;

CREATE TRIGGER event_set_updated_at BEFORE
UPDATE ON app_wiki.event FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- !section
-- section RLS POLICIES
-- anchor document
CREATE POLICY "document_select_all" ON app_wiki.document FOR
SELECT
  USING (TRUE)
;

-- anchor changelog
CREATE POLICY "changelog_select_all" ON app_wiki.changelog FOR
SELECT
  USING (TRUE)
;

-- anchor rule
CREATE POLICY "rule_select_all" ON app_wiki.rule FOR
SELECT
  USING (TRUE)
;

-- anchor news
CREATE POLICY "news_select_all" ON app_wiki.news FOR
SELECT
  USING (TRUE)
;

-- anchor event
CREATE POLICY "event_select_all" ON app_wiki.event FOR
SELECT
  USING (TRUE)
;

-- !section