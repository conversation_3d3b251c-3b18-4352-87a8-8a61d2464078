# Commands

## Running SQL Files

AFTER SCHEMA CHANGES, ALWAYS RUN `pnpm sql:run <path/to/file.sql>` COMMAND.
If successful, run `pnpm supabase:reset-db-local` to reset the database. If there is any error, try fixing and rerun the commands. Finally if everything is okay, run `pnpm supabase:types`

## Running Tests

Do not run test commands in watch mode.

### All Tests

```bash
pnpm test supabase/__tests__
```

### Individual File

```bash
pnpm test supabase/__tests__/app_access.role.test.ts
```
