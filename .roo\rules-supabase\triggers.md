# Triggers

## Updated At

Always add triggers to set `updated_at` columns.

```sql
CREATE TRIGGER application_set_updated_at
BEFORE UPDATE ON app_provider.application
FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at();
```

## Locale Columns

Use `validate_locale_columns` function with triggers for JSONB columns storing localized content.

```sql
CREATE TRIGGER service_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.service FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;
```
