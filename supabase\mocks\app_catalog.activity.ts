import { afterAll, beforeAll } from "vitest";
import { MockUser } from "./auth.user";

export type MockCatalogActivity = {
  id?: string;
};

type MockCatalogActivityParams = {
  admin: MockUser;
};

export async function createCatalogActivity(
  { admin }: MockCatalogActivityParams,
  mockActivity: MockCatalogActivity
) {
  if (!admin.client) throw new Error("Admin client is undefined");

  const { data: activity } = await admin.client
    .schema("app_catalog")
    .from("activity")
    .insert({
      name: { en: "Test Activity" }
    })
    .select()
    .single();

  mockActivity.id = activity?.id;
}

export async function cleanCatalogActivity(
  { admin }: MockCatalogActivityParams,
  activity: MockCatalogActivity
) {
  if (!admin.client) throw new Error("Admin client is undefined");
  if (!activity.id) throw new Error("Activity ID is undefined");

  await admin.client
    .schema("app_catalog")
    .from("activity")
    .delete()
    .eq("id", activity.id);
}

export function mockCatalogActivity(params: MockCatalogActivityParams) {
  const activity: MockCatalogActivity = {};

  beforeAll(async () => {
    await createCatalogActivity(params, activity);
  });

  afterAll(async () => {
    await cleanCatalogActivity(params, activity);
  });

  return activity;
}
