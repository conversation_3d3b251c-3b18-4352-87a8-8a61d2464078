import type { MockUser } from "../../mocks/auth.user";

type Permissions<T extends string> = Record<
  T,
  {
    view: boolean;
    insert: boolean;
    update: boolean;
    delete: boolean;
  }
>;
type Matrix<P extends string> = {
  user: MockUser;
  permissions: Permissions<P>;
};
export function createRoleTestMatrix<R extends string, P extends string>(
  matrix: Record<R, Matrix<P>>
) {
  return (Object.keys(matrix) as R[]).map((role) => ({
    role,
    ...matrix[role]
  }));
}
